import { Company, Allocation, UserType, CompanyStatus } from '@prisma/client'

// Database types
export type { Company, Allocation, UserType, CompanyStatus }

export interface CompanyWithAllocations extends Company {
  allocations: Allocation[]
  loginToken: string
}

// Auth types
export interface User {
  id: string
  email: string
  role: 'admin' | 'company'
  companyId?: string
}

export interface LoginCredentials {
  email?: string
  loginToken?: string
  password: string
}

// API Response types
export interface ApiResponse<T = unknown> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// Form types
export interface CompanyFormData {
  name: string
  organizationId: string
  email: string
  password: string
  userType: UserType
  validity: number
  description?: string
  document?: File
}

export interface DeviceAllocationData {
  companyId: string
  deviceSerialNos: string[]
}

// Dashboard stats
export interface AdminDashboardStats {
  totalCompanies: number
  activeCompanies: number
  expiredCompanies: number
  totalDevices: number
  allocatedDevices: number
  unallocatedDevices: number
}

export interface CompanyDashboardStats {
  allocatedDevices: number
  daysRemaining: number
  accountStatus: CompanyStatus
  expirationDate: string
}

// Table types
export interface TableColumn<T = unknown> {
  key: string
  label: string
  sortable?: boolean
  render?: (value: unknown, row: T) => React.ReactNode
}

export interface TableProps<T = unknown> {
  data: T[]
  columns: TableColumn<T>[]
  loading?: boolean
  searchable?: boolean
  selectable?: boolean
  onSelectionChange?: (selectedRows: T[]) => void
  pagination?: boolean
  pageSize?: number
}

// Device types (from external API)
export interface Device {
  serialNo: string
  name: string
  modelName: string
  modelNo: string
  location: string
  status: 'online' | 'offline' | 'unknown'
  userCount: number
  logCount: number
  isFace: boolean
  isFinger: boolean
  isCard: boolean
  lastUpdateDate: string
  allocatedTo?: string[]
}

// Notification types
export interface NotificationData {
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message: string
  duration?: number
}

// File upload types
export interface FileUploadResult {
  success: boolean
  url?: string
  error?: string
}

// Search and filter types
export interface SearchFilters {
  query?: string
  status?: CompanyStatus
  userType?: UserType
  dateRange?: {
    from: Date
    to: Date
  }
}

// Bulk action types
export interface BulkAction {
  id: string
  label: string
  icon?: React.ReactNode
  action: (selectedItems: unknown[]) => void | Promise<void>
  confirmMessage?: string
  destructive?: boolean
}

// Theme types
export interface ThemeConfig {
  mode: 'light' | 'dark'
  primaryColor: string
  borderRadius: number
}

// Settings types
export interface UserSettings {
  theme: ThemeConfig
  notifications: {
    email: boolean
    browser: boolean
  }
  timezone: string
}

// Error types
export interface AppError {
  code: string
  message: string
  details?: unknown
}

// Pagination types
export interface PaginationInfo {
  page: number
  pageSize: number
  total: number
  totalPages: number
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: PaginationInfo
}
