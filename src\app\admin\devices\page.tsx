"use client";

import { DashboardLayout } from "@/components/shared/dashboard-layout";
import { DeviceAllocationModal } from "@/components/features/device-allocation-modal";
import { EditDeviceDialog } from "@/components/features/edit-device-dialog";
import { SyncDevicesModal } from "@/components/features/sync-devices-modal";
import { DeviceStatsCards } from "@/components/features/device-stats-cards";
import { DeviceTableSection } from "@/components/features/device-table-section";
import { DevicePageHeader } from "@/components/features/device-page-header";
import { useDeviceColumns } from "@/components/features/device-table-columns";
import { useDeviceBulkActions } from "@/components/features/device-bulk-actions";
import { useRouter } from "next/navigation";
import { useDeviceManagement } from "@/hooks/useDeviceManagementUpdated";
import { toast } from "react-toastify";
import { useState } from "react";

export default function AdminDevicesPage() {
  const router = useRouter();

  // Local state for edit device dialog
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [editingDevice, setEditingDevice] = useState<any>(null);

  // Local state for sync devices modal
  const [showSyncModal, setShowSyncModal] = useState(false);

  // Use the updated device management hook
  const {
    devices,
    loading,
    filters,
    pagination,
    selectedDevices,
    deviceStatuses,
    statusLoading,

    showAddDialog,
    showAllocationModal,
    allocationMode,
    showStatusTooltip,
    isHydrated,
    renderKey,
    statusHistory,
    deviceFormData,
    setSelectedDevices,
    setShowAddDialog,
    setShowAllocationModal,
    setAllocationMode,
    setDeviceFormData,
    handlePageChange,
    handleSearch,
    handleSort,
    handleRefreshAllDevicesStatus,
    handleDeviceAction,
    handleBulkDelete,
    refetch,
  } = useDeviceManagement();

  // Enhanced device action handler
  const enhancedDeviceAction = async (
    action: string,
    deviceSlno: string,
    additionalData?: Record<string, unknown>
  ) => {
    if (action === "edit") {
      // Find the device and open edit dialog
      const device = devices.find((d) => d.serialNumber === deviceSlno);
      if (device) {
        setEditingDevice(device);
        setShowEditDialog(true);
      }
      return;
    }

    if (action === "moreActions") {
      // Navigate to device actions page
      router.push(`/admin/devices/${deviceSlno}/actions`);
      return;
    }

    // For other actions, use the original handler
    await handleDeviceAction(action, deviceSlno, additionalData);
  };

  // Get table columns
  const columns = useDeviceColumns({
    deviceStatuses,
    statusHistory,
    isHydrated,
    onDeviceAction: enhancedDeviceAction,
  });

  // Get bulk actions
  const bulkActions = useDeviceBulkActions({
    selectedDevices,
    onRefreshAllDevicesStatus: handleRefreshAllDevicesStatus,
    onSetAllocationMode: setAllocationMode,
    onSetShowAllocationModal: setShowAllocationModal,
    onBulkDelete: handleBulkDelete,
  });

  // Add device handler
  const handleAddDevice = async () => {
    try {
      const response = await fetch("/api/admin/devices", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(deviceFormData),
      });

      if (response.ok) {
        toast.success("Device added successfully");
        setShowAddDialog(false);
        setDeviceFormData({
          deviceSlno: "",
          deviceName: "",
          modelNo: "",
          timeZoneId: "",
          location: "",
          endpointUrl: "",
        });
        refetch(); // Refresh data using TanStack Query
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to add device");
      }
    } catch {
      toast.error("Failed to add device");
    }
  };

  return (
    <DashboardLayout userRole="admin" userEmail="<EMAIL>">
      <div className="space-y-6">
        {/* Header */}
        <DevicePageHeader
          showAddDialog={showAddDialog}
          showStatusTooltip={showStatusTooltip}
          statusLoading={statusLoading}
          deviceFormData={deviceFormData}
          onShowAddDialogChange={setShowAddDialog}
          onRefreshAllDevicesStatus={handleRefreshAllDevicesStatus}
          onReloadData={() => refetch()}
          onDeviceFormDataChange={setDeviceFormData}
          onAddDevice={handleAddDevice}
          onSyncDevices={() => setShowSyncModal(true)}
        />

        {/* Stats Cards */}
        <DeviceStatsCards
          devices={devices}
          deviceStatuses={deviceStatuses}
          pagination={pagination}
        />

        {/* Devices Table */}
        <DeviceTableSection
          devices={devices}
          columns={columns}
          loading={loading}
          pagination={pagination}
          selectedDevices={selectedDevices}
          searchQuery={filters.search}
          sortBy={filters.sortBy}
          sortOrder={filters.sortOrder}
          bulkActions={bulkActions}
          onSelectionChange={setSelectedDevices}
          onPageChange={handlePageChange}
          onSearch={handleSearch}
          onSort={handleSort}
          renderKey={renderKey}
        />

        {/* Device Allocation Modal */}
        <DeviceAllocationModal
          open={showAllocationModal}
          onOpenChange={setShowAllocationModal}
          selectedDevices={selectedDevices.map((device) => device.serialNumber)}
          onSuccess={() => {
            refetch(); // Refresh data using TanStack Query
            setSelectedDevices([]);
          }}
          mode={allocationMode}
        />

        {/* Edit Device Dialog */}
        <EditDeviceDialog
          open={showEditDialog}
          onOpenChange={setShowEditDialog}
          device={editingDevice}
          onSave={async (deviceData) => {
            try {
              const response = await fetch("/api/admin/devices/edit", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify(deviceData),
              });

              if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error || "Failed to update device");
              }

              // Refresh devices list
              await refetch();
              setEditingDevice(null);
            } catch (error) {
              throw error;
            }
          }}
        />

        {/* Sync Devices Modal */}
        <SyncDevicesModal
          open={showSyncModal}
          onOpenChange={setShowSyncModal}
          devices={devices}
          onSuccess={() => {
            refetch(); // Refresh data after sync
          }}
        />
      </div>
    </DashboardLayout>
  );
}
