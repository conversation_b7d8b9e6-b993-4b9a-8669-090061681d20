"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Clock,
  Download,
  Trash2,
  Search,
  User,
  Calendar,
  AlertTriangle,
} from "lucide-react";
import { toast } from "sonner";
import DatePicker, { DateRangePicker } from "@/components/DatePicker";

interface AttendanceLog {
  enrollmentNo: string;
  deviceSlno: string;
  punchDateTime: string;
  inOutValue: number;
  modeValue: number;
  eventValue: number;
}

interface AttendanceLogsViewProps {
  deviceId: string;
  isDeviceOnline: () => boolean;
}

export function AttendanceLogsView({
  deviceId,
  isDeviceOnline,
}: AttendanceLogsViewProps) {
  const [logs, setLogs] = useState<AttendanceLog[]>([]);
  const [loading, setLoading] = useState(false);
  const [startDate, setStartDate] = useState<Date>(() => {
    // Default to last 7 days
    const date = new Date();
    date.setDate(date.getDate() - 30);
    return date;
  });
  const [endDate, setEndDate] = useState<Date>(() => {
    // Default to current date
    return new Date();
  });

  const checkDeviceOnlineBeforeAction = (actionName: string) => {
    if (!isDeviceOnline()) {
      toast.error(`Cannot ${actionName} - Device is offline`);
      return false;
    }
    return true;
  };

  const handleGetLogs = async () => {
    if (!checkDeviceOnlineBeforeAction("get attendance logs")) {
      return;
    }

    if (!startDate || !endDate) {
      toast.error("Please select both from and to dates");
      return;
    }

    if (startDate > endDate) {
      toast.error("From date cannot be later than to date");
      return;
    }

    setLoading(true);
    try {
      toast.info("Retrieving attendance logs from device...");

      const response = await fetch(
        `/api/admin/devices/${deviceId}/attendance-logs`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            action: "getLogs",
            fromDate:
              startDate.toISOString().slice(0, 16).replace("T", " ") + ":00",
            toDate:
              endDate.toISOString().slice(0, 16).replace("T", " ") + ":00",
          }),
        }
      );

      const data = await response.json();

      if (response.ok && data.success) {
        setLogs(data.data || []);
        toast.success(
          `Retrieved ${data.data?.length || 0} attendance logs successfully`
        );
      } else {
        toast.error(data.error || "Failed to get attendance logs");
      }
    } catch (error) {
      console.error("Get logs error:", error);
      toast.error("Failed to get attendance logs");
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadLogs = () => {
    if (logs.length === 0) {
      toast.error("No logs to download");
      return;
    }

    // Create CSV content
    const headers = [
      "Enrollment No",
      "Device Serial",
      "Punch Date & Time",
      "In/Out Value",
      "Mode Value",
      "Event Value",
    ];

    const csvContent = [
      headers.join(","),
      ...logs.map((log) =>
        [
          log.enrollmentNo,
          log.deviceSlno,
          `"${log.punchDateTime}"`,
          log.inOutValue,
          log.modeValue,
          log.eventValue,
        ].join(",")
      ),
    ].join("\n");

    // Create and download file
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute(
      "download",
      `attendance_logs_${deviceId}_${new Date().toISOString().slice(0, 10)}.csv`
    );
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast.success("Attendance logs downloaded successfully");
  };

  const handleClearLogs = async () => {
    if (!checkDeviceOnlineBeforeAction("clear attendance logs")) {
      return;
    }

    if (
      !confirm(
        "Are you sure you want to clear ALL attendance logs from this device? This action cannot be undone."
      )
    ) {
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(
        `/api/admin/devices/${deviceId}/attendance-logs`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            action: "clearLogs",
          }),
        }
      );

      const data = await response.json();

      if (response.ok && data.success) {
        setLogs([]); // Clear local logs
        toast.success(data.message);
      } else {
        toast.error(data.error || "Failed to clear attendance logs");
      }
    } catch (error) {
      console.error("Clear logs error:", error);
      toast.error("Failed to clear attendance logs");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5" />
          Attendance Logs
        </CardTitle>
        <CardDescription>
          View and manage attendance logs for this device
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Date Range Selection */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
          <div className="space-y-2 md:col-span-2">
            <Label>Select Date Range</Label>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div className="space-y-2">
                <DatePicker
                  value={startDate}
                  onChange={(date) => setStartDate(date || new Date())}
                  placeholder="Select start date"
                  className="w-full"
                  dateFormat="MM/dd/yyyy"
                />
              </div>
              <div className="space-y-2">
                <DatePicker
                  value={endDate}
                  onChange={(date) => setEndDate(date || new Date())}
                  placeholder="Select end date"
                  className="w-full"
                  dateFormat="MM/dd/yyyy"
                />
              </div>
            </div>
          </div>
          <div className="flex items-end">
            <Button
              onClick={handleGetLogs}
              disabled={loading || !isDeviceOnline()}
              className="w-full"
            >
              <Search className="h-4 w-4 mr-2" />
              {loading ? "Getting Logs..." : "Get Logs"}
            </Button>
          </div>
          <div className="flex items-end">
            <Button
              variant="outline"
              onClick={handleDownloadLogs}
              disabled={logs.length === 0}
              className="w-full"
            >
              <Download className="h-4 w-4 mr-2" />
              Download CSV
            </Button>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between items-start">
          <div className="flex flex-col sm:flex-row sm:items-center gap-2">
            <Badge variant="outline" className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              {logs.length} logs found
            </Badge>
            {!isDeviceOnline() && (
              <Badge variant="destructive" className="flex items-center gap-1">
                <AlertTriangle className="h-3 w-3" />
                Device Offline
              </Badge>
            )}
          </div>
          <Button
            variant="destructive"
            size="sm"
            onClick={handleClearLogs}
            disabled={loading || !isDeviceOnline()}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Clear All Logs
          </Button>
        </div>

        {/* Logs Table */}
        {logs.length > 0 ? (
          <div className="w-full border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Enrollment No</TableHead>
                  <TableHead>Punch Date & Time</TableHead>
                  <TableHead>In/Out Value</TableHead>
                  <TableHead>Mode Value</TableHead>
                  <TableHead>Event Value</TableHead>
                  <TableHead>Device Serial</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {logs.map((log, index) => (
                  <TableRow key={attendance.id}>
                    <TableCell className="font-medium">
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-gray-500" />
                        {log.enrollmentNo}
                      </div>
                    </TableCell>
                    <TableCell>{log.punchDateTime}</TableCell>
                    <TableCell>
                      <Badge variant="outline">{log.inOutValue}</Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary">{log.modeValue}</Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant="default">{log.eventValue}</Badge>
                    </TableCell>
                    <TableCell className="text-sm text-gray-600">
                      {log.deviceSlno}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <Clock className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p className="text-lg font-medium">No attendance logs found</p>
            <p className="text-sm">
              {`Select a date range and click "Get Logs" to retrieve attendance
              data`}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
