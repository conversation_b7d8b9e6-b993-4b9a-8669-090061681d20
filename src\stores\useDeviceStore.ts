import { create } from "zustand";
import { devtools } from "zustand/middleware";

export interface DeviceFilters {
  search: string;
  status?: string;
  allocation?: string;
  sortBy: string;
  sortOrder: "asc" | "desc";
}

export interface DevicePagination {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

export interface Device {
  id: string;
  name: string;
  serialNumber: string;
  model: string;
  modelName: string;
  location: string;
  ipAddress: string;
  port: number;
  status: string;
  lastSeen?: string | null;
  firmwareVersion: string;
  totalUsers: number;
  timeZone: number;
  logCount: number;
  features: {
    faceRecognition: boolean;
    fingerprintScanner: boolean;
    cardReader: boolean;
    temperatureCheck: boolean;
  };
  biometricCounts: {
    fingerprints: number;
    faces: number;
    cards: number;
    passwords: number;
  };
  createdAt: string;
  updatedAt: string;
  allocatedCompany?: string | null;
}

export type DeviceStatus = "ONLINE" | "OFFLINE" | "UNKNOWN" | "LOADING";

interface DeviceStoreState {
  // UI State
  filters: DeviceFilters;
  pagination: DevicePagination;
  selectedDevices: Device[];

  // Device Status State
  deviceStatuses: Record<string, DeviceStatus>;
  statusLoading: boolean;
  statusLastFetched: number | null;

  // UI Actions
  setSearch: (search: string) => void;
  setStatus: (status?: string) => void;
  setAllocation: (allocation?: string) => void;
  setSorting: (sortBy: string, sortOrder?: "asc" | "desc") => void;
  setPage: (page: number) => void;
  setPageSize: (pageSize: number) => void;
  setPagination: (pagination: Partial<DevicePagination>) => void;
  setSelectedDevices: (devices: Device[]) => void;
  toggleDeviceSelection: (device: Device) => void;
  selectAllDevices: (devices: Device[]) => void;
  clearSelection: () => void;
  resetFilters: () => void;

  // Device Status Actions
  setDeviceStatus: (serialNumber: string, status: DeviceStatus) => void;
  setMultipleDeviceStatuses: (statuses: Record<string, DeviceStatus>) => void;
  setStatusLoading: (loading: boolean) => void;
  setStatusLastFetched: (timestamp: number) => void;
  clearDeviceStatuses: () => void;
}

const initialFilters: DeviceFilters = {
  search: "",
  status: undefined,
  allocation: undefined,
  sortBy: "name",
  sortOrder: "asc",
};

const initialPagination: DevicePagination = {
  page: 1,
  pageSize: 10,
  total: 0,
  totalPages: 0,
};

export const useDeviceStore = create<DeviceStoreState>()(
  devtools(
    (set) => ({
      // Initial state
      filters: initialFilters,
      pagination: initialPagination,
      selectedDevices: [],
      deviceStatuses: {},
      statusLoading: false,
      statusLastFetched: null,

      // Filter actions
      setSearch: (search: string) =>
        set(
          (state) => ({
            filters: { ...state.filters, search },
            pagination: { ...state.pagination, page: 1 }, // Reset to first page on search
          }),
          false,
          "setSearch"
        ),

      setStatus: (status?: string) =>
        set(
          (state) => ({
            filters: { ...state.filters, status },
            pagination: { ...state.pagination, page: 1 },
          }),
          false,
          "setStatus"
        ),

      setAllocation: (allocation?: string) =>
        set(
          (state) => ({
            filters: { ...state.filters, allocation },
            pagination: { ...state.pagination, page: 1 },
          }),
          false,
          "setAllocation"
        ),

      setSorting: (sortBy: string, sortOrder?: "asc" | "desc") => {
        set(
          (state) => {
            const currentFilters = state.filters;
            const newSortOrder =
              sortOrder ||
              (currentFilters.sortBy === sortBy &&
              currentFilters.sortOrder === "asc"
                ? "desc"
                : "asc");

            return {
              filters: { ...currentFilters, sortBy, sortOrder: newSortOrder },
              pagination: { ...state.pagination, page: 1 },
            };
          },
          false,
          "setSorting"
        );
      },

      // Pagination actions
      setPage: (page: number) =>
        set(
          (state) => ({
            pagination: { ...state.pagination, page },
          }),
          false,
          "setPage"
        ),

      setPageSize: (pageSize: number) =>
        set(
          (state) => ({
            pagination: { ...state.pagination, pageSize, page: 1 },
          }),
          false,
          "setPageSize"
        ),

      setPagination: (pagination: Partial<DevicePagination>) =>
        set(
          (state) => ({
            pagination: { ...state.pagination, ...pagination },
          }),
          false,
          "setPagination"
        ),

      // Selection actions
      setSelectedDevices: (devices: Device[]) =>
        set({ selectedDevices: devices }, false, "setSelectedDevices"),

      toggleDeviceSelection: (device: Device) =>
        set(
          (state) => {
            const isSelected = state.selectedDevices.some(
              (d) => d.id === device.id
            );
            const selectedDevices = isSelected
              ? state.selectedDevices.filter((d) => d.id !== device.id)
              : [...state.selectedDevices, device];
            return { selectedDevices };
          },
          false,
          "toggleDeviceSelection"
        ),

      selectAllDevices: (devices: Device[]) =>
        set({ selectedDevices: devices }, false, "selectAllDevices"),

      clearSelection: () =>
        set({ selectedDevices: [] }, false, "clearSelection"),

      resetFilters: () =>
        set(
          {
            filters: initialFilters,
            pagination: initialPagination,
            selectedDevices: [],
          },
          false,
          "resetFilters"
        ),

      // Device Status actions
      setDeviceStatus: (serialNumber: string, status: DeviceStatus) =>
        set(
          (state) => ({
            deviceStatuses: { ...state.deviceStatuses, [serialNumber]: status },
          }),
          false,
          "setDeviceStatus"
        ),

      setMultipleDeviceStatuses: (statuses: Record<string, DeviceStatus>) =>
        set(
          (state) => ({
            deviceStatuses: { ...state.deviceStatuses, ...statuses },
          }),
          false,
          "setMultipleDeviceStatuses"
        ),

      setStatusLoading: (loading: boolean) =>
        set({ statusLoading: loading }, false, "setStatusLoading"),

      setStatusLastFetched: (timestamp: number) =>
        set({ statusLastFetched: timestamp }, false, "setStatusLastFetched"),

      clearDeviceStatuses: () =>
        set(
          { deviceStatuses: {}, statusLastFetched: null },
          false,
          "clearDeviceStatuses"
        ),
    }),
    {
      name: "device-store",
    }
  )
);
