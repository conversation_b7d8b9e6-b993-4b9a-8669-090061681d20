"use client";

import { useState, useEffect, useRef } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import DatePicker from "@/components/DatePicker";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import {
  Info,
  Users,
  Clock,
  Settings,
  Refresh<PERSON>w,
  CheckCircle,
  XCircle,
  Upload,
  UserPlus,
  Shield,
  Calendar,
  Image,
} from "lucide-react";
import { toast } from "sonner";
import { AttendanceLogsView } from "@/components/features/attendance-logs-view";
import { DeviceActionsView } from "@/components/features/device-actions-view";

interface DeviceActionsContentProps {
  deviceId: string;
  userRole?: "admin" | "company";
}

interface DeviceInfo {
  deviceName?: string;
  serialNumber?: string;
  model?: string;
  location?: string;
  ipAddress?: string;
  port?: number;
  status?: string;
  lastSeen?: string;
  firmware?: string;
  capacity?: number;
  usedSpace?: number;
  temperature?: number;
  uptime?: string;
}

interface UserFormData {
  userId: string;
  userName: string;
  userType: "user" | "admin";
  validFrom: Date | null;
  validTo: Date | null;
  faceData: string;
  fingerData: string;
}

interface DeviceUser {
  id: string;
  enrollmentNo: string;
  name: string;
  deviceSerial: string;
  isAdmin: boolean;
  biometrics: {
    hasFace: boolean;
    hasFingerprint: boolean;
    hasCard: boolean;
    hasPassword: boolean;
  };
  isEnabled: boolean;
  privilege: number;
  createdAt: string;
  updatedAt: string;
}

export function DeviceActionsContent({
  deviceId,
  userRole = "admin",
}: DeviceActionsContentProps) {
  const [activeTab, setActiveTab] = useState("info");
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [userData, setUserData] = useState<DeviceUser[]>([]);
  const [deviceStatus, setDeviceStatus] = useState<
    "ONLINE" | "OFFLINE" | "UNKNOWN" | "LOADING"
  >("LOADING");
  const [userFormData, setUserFormData] = useState<UserFormData>({
    userId: "",
    userName: "",
    userType: "user",
    validFrom: null,
    validTo: null,
    faceData: "",
    fingerData: "",
  });

  const [setValidityDialog, setSetValidityDialog] = useState({
    open: false,
    userId: "",
    userName: "",
    fromDate: null as Date | null,
    toDate: null as Date | null,
  });

  const fileInputRef = useRef<HTMLInputElement>(null);

  // Load device info and status when component mounts
  useEffect(() => {
    loadDeviceInfo();
    checkDeviceStatus();
  }, [deviceId]);

  const loadDeviceInfo = async () => {
    setLoading(true);
    try {
      // Use appropriate API based on user role
      const apiPath =
        userRole === "company"
          ? `/api/dashboard/devices/${deviceId}/info`
          : `/api/admin/devices/${deviceId}/info`;

      const response = await fetch(apiPath);
      if (response.ok) {
        const data = await response.json();
        setDeviceInfo(data.deviceInfo);
      } else {
        toast.error("Failed to load device information");
      }
    } catch (error) {
      console.error("Error loading device info:", error);
      toast.error("Failed to load device information");
    } finally {
      setLoading(false);
    }
  };

  const checkDeviceStatus = async () => {
    try {
      setDeviceStatus("LOADING");

      // Use appropriate API based on user role
      const apiPath =
        userRole === "company"
          ? `/api/dashboard/devices/${deviceId}/status`
          : `/api/admin/devices/${deviceId}/status`;

      const response = await fetch(apiPath);
      if (response.ok) {
        const data = await response.json();
        setDeviceStatus(data.status);
        console.log(`Device ${deviceId} status: ${data.status}`);
      } else {
        console.error("Failed to check device status");
        setDeviceStatus("UNKNOWN");
      }
    } catch (error) {
      console.error("Error checking device status:", error);
      setDeviceStatus("UNKNOWN");
    }
  };

  const isDeviceOnline = () => {
    return deviceStatus === "ONLINE";
  };

  const checkDeviceOnlineBeforeAction = (actionName: string) => {
    if (!isDeviceOnline()) {
      toast.error(
        `Cannot ${actionName} - Device is ${deviceStatus.toLowerCase()}. Please ensure the device is online before performing this action.`
      );
      return false;
    }
    return true;
  };

  const handleGetUserData = async () => {
    if (!checkDeviceOnlineBeforeAction("get user data")) {
      return;
    }

    setLoading(true);
    try {
      // Use appropriate API based on user role
      const apiPath =
        userRole === "company"
          ? `/api/dashboard/devices/${deviceId}/users`
          : `/api/admin/devices/${deviceId}/users`;

      const response = await fetch(apiPath, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          action: "getUserData",
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setUserData(data.data || []);
        toast.success(`Retrieved ${data.data?.length || 0} users successfully`);
        console.log("User data:", data);
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to get user data");
      }
    } catch (error) {
      console.error("Error getting user data:", error);
      toast.error("Failed to get user data");
    } finally {
      setLoading(false);
    }
  };

  const handleClearAllUsers = async () => {
    if (!checkDeviceOnlineBeforeAction("clear all users")) {
      return;
    }

    setLoading(true);
    try {
      // Use appropriate API based on user role
      const apiPath =
        userRole === "company"
          ? `/api/dashboard/devices/${deviceId}/users`
          : `/api/admin/devices/${deviceId}/users`;

      const response = await fetch(apiPath, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          action: "clearAllUsers",
        }),
      });

      if (response.ok) {
        setUserData([]); // Clear local user data
        toast.success("All users cleared successfully");
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to clear users");
      }
    } catch (error) {
      console.error("Error clearing users:", error);
      toast.error("Failed to clear users");
    } finally {
      setLoading(false);
    }
  };

  const handleClearAllAdmins = async () => {
    if (!checkDeviceOnlineBeforeAction("clear all admins")) {
      return;
    }

    setLoading(true);
    try {
      // Use appropriate API based on user role
      const apiPath =
        userRole === "company"
          ? `/api/dashboard/devices/${deviceId}/users`
          : `/api/admin/devices/${deviceId}/users`;

      const response = await fetch(apiPath, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          action: "clearAllAdmins",
        }),
      });

      if (response.ok) {
        // Update local user data to remove admin status
        setUserData((prev) =>
          prev.map((user) => ({ ...user, isAdmin: false }))
        );
        toast.success("All admins cleared successfully");
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to clear admins");
      }
    } catch (error) {
      console.error("Error clearing admins:", error);
      toast.error("Failed to clear admins");
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteUser = async (userId: string) => {
    if (!checkDeviceOnlineBeforeAction("delete user")) {
      return;
    }

    if (
      !confirm(
        `Are you sure you want to delete user ${userId}? This action cannot be undone.`
      )
    ) {
      return;
    }

    setLoading(true);
    try {
      // Use appropriate API based on user role
      const apiPath =
        userRole === "company"
          ? `/api/dashboard/devices/${deviceId}/users`
          : `/api/admin/devices/${deviceId}/users`;

      const response = await fetch(apiPath, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          action: "deleteUser",
          userId: userId,
        }),
      });

      if (response.ok) {
        // Remove user from local state
        setUserData((prev) =>
          prev.filter((user) => user.enrollmentNo !== userId)
        );
        toast.success(`User ${userId} deleted successfully`);
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to delete user");
      }
    } catch (error) {
      console.error("Error deleting user:", error);
      toast.error("Failed to delete user");
    } finally {
      setLoading(false);
    }
  };

  const handleToggleAdmin = async (
    userId: string,
    isCurrentlyAdmin: boolean
  ) => {
    if (!checkDeviceOnlineBeforeAction("toggle admin status")) {
      return;
    }

    setLoading(true);
    try {
      const action = isCurrentlyAdmin ? "removeAdmin" : "makeAdmin";

      // Use appropriate API based on user role
      const apiPath =
        userRole === "company"
          ? `/api/dashboard/devices/${deviceId}/users`
          : `/api/admin/devices/${deviceId}/users`;

      const response = await fetch(apiPath, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action,
          userId,
        }),
      });

      if (response.ok) {
        // Update local state
        setUserData((prev) =>
          prev.map((user) =>
            user.enrollmentNo === userId
              ? { ...user, isAdmin: !isCurrentlyAdmin }
              : user
          )
        );
        toast.success(
          `User ${userId} ${
            isCurrentlyAdmin ? "is no longer an admin" : "is now an admin"
          }`
        );
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to update admin status");
      }
    } catch (error) {
      console.error("Error toggling admin status:", error);
      toast.error("Failed to update admin status");
    } finally {
      setLoading(false);
    }
  };

  const handleToggleUserStatus = async (
    userId: string,
    isCurrentlyEnabled: boolean
  ) => {
    if (!checkDeviceOnlineBeforeAction("toggle user status")) {
      return;
    }

    setLoading(true);
    try {
      const action = isCurrentlyEnabled ? "disableUser" : "enableUser";

      // Use appropriate API based on user role
      const apiPath =
        userRole === "company"
          ? `/api/dashboard/devices/${deviceId}/users`
          : `/api/admin/devices/${deviceId}/users`;

      const response = await fetch(apiPath, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action,
          userId,
        }),
      });

      if (response.ok) {
        // Update local state
        setUserData((prev) =>
          prev.map((user) =>
            user.enrollmentNo === userId
              ? { ...user, isEnabled: !isCurrentlyEnabled }
              : user
          )
        );
        toast.success(
          `User ${userId} ${
            isCurrentlyEnabled ? "disabled" : "enabled"
          } successfully`
        );
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to update user status");
      }
    } catch (error) {
      console.error("Error toggling user status:", error);
      toast.error("Failed to update user status");
    } finally {
      setLoading(false);
    }
  };

  const handleSetValidity = async () => {
    if (!setValidityDialog.fromDate || !setValidityDialog.toDate) {
      toast.error("Please select both from and to dates");
      return;
    }

    if (!checkDeviceOnlineBeforeAction("set user validity")) {
      return;
    }

    setLoading(true);
    try {
      const fromDate = setValidityDialog.fromDate
        .toISOString()
        .slice(0, 19)
        .replace("T", " ");
      const toDate = setValidityDialog.toDate
        .toISOString()
        .slice(0, 19)
        .replace("T", " ");

      // Use appropriate API based on user role
      const apiPath =
        userRole === "company"
          ? `/api/dashboard/devices/${deviceId}/users`
          : `/api/admin/devices/${deviceId}/users`;

      const response = await fetch(apiPath, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "setValidity",
          userId: setValidityDialog.userId,
          fromDate,
          toDate,
        }),
      });

      if (response.ok) {
        toast.success(`Validity set for user ${setValidityDialog.userId}`);
        setSetValidityDialog({
          open: false,
          userId: "",
          userName: "",
          fromDate: null,
          toDate: null,
        });
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to set user validity");
      }
    } catch (error) {
      console.error("Error setting user validity:", error);
      toast.error("Failed to set user validity");
    } finally {
      setLoading(false);
    }
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.type.startsWith("image/")) {
      toast.error("Please select a valid image file");
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      const img = new window.Image();
      img.onload = () => {
        const canvas = document.createElement("canvas");
        const ctx = canvas.getContext("2d");

        // Resize image to reasonable dimensions
        const maxWidth = 300;
        const maxHeight = 300;
        let { width, height } = img;

        if (width > height) {
          if (width > maxWidth) {
            height = (height * maxWidth) / width;
            width = maxWidth;
          }
        } else {
          if (height > maxHeight) {
            width = (width * maxHeight) / height;
            height = maxHeight;
          }
        }

        canvas.width = width;
        canvas.height = height;

        ctx?.drawImage(img, 0, 0, width, height);

        // Convert to base64 with compression
        const compressedBase64 = canvas.toDataURL("image/jpeg", 0.8);
        setUserFormData((prev) => ({
          ...prev,
          faceData: compressedBase64.split(",")[1], // Remove data:image/jpeg;base64, prefix
        }));

        toast.success("Face image uploaded and compressed");
      };
      img.src = e.target?.result as string;
    };
    reader.readAsDataURL(file);
  };

  const handleAddUser = async () => {
    if (!userFormData.userId || !userFormData.userName) {
      toast.error("Please fill in User ID and User Name");
      return;
    }

    if (!checkDeviceOnlineBeforeAction("add user")) {
      return;
    }

    setLoading(true);
    try {
      // Use appropriate API based on user role
      const apiPath =
        userRole === "company"
          ? `/api/dashboard/devices/${deviceId}/users`
          : `/api/admin/devices/${deviceId}/users`;

      const response = await fetch(apiPath, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          action: "addUser",
          userId: userFormData.userId,
          userName: userFormData.userName,
        }),
      });

      if (response.ok) {
        toast.success("User added successfully");
        // Reset form
        setUserFormData({
          userId: "",
          userName: "",
          userType: "user",
          validFrom: null,
          validTo: null,
          faceData: "",
          fingerData: "",
        });
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
      } else {
        const error = await response.json();
        toast.error(error.error || "Failed to add user");
      }
    } catch (error) {
      console.error("Error adding user:", error);
      toast.error("Failed to add user");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="w-full max-w-6xl mx-auto">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full min-h-max space-y-2 grid-cols-2 md:grid-cols-4">
          <TabsTrigger value="info" className="flex items-center gap-2">
            <Info className="h-4 w-4" />
            Device Info
          </TabsTrigger>
          <TabsTrigger value="users" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Device Users
          </TabsTrigger>
          <TabsTrigger value="logs" className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Attendance Logs
          </TabsTrigger>
          <TabsTrigger value="actions" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Device Actions
          </TabsTrigger>
        </TabsList>

        {/* Device Info Tab */}
        <TabsContent value="info" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Info className="h-5 w-5" />
                Device Information
              </CardTitle>
              <CardDescription>
                View detailed information about this device
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <RefreshCw className="h-6 w-6 animate-spin" />
                  <span className="ml-2">Loading device information...</span>
                </div>
              ) : deviceInfo ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Device Name
                      </Label>
                      <p className="text-sm font-medium">
                        {deviceInfo.deviceName || "N/A"}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Serial Number
                      </Label>
                      <p className="text-sm font-medium">
                        {deviceInfo.serialNumber || deviceId}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Model
                      </Label>
                      <p className="text-sm font-medium">
                        {deviceInfo.model || "N/A"}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Location
                      </Label>
                      <p className="text-sm font-medium">
                        {deviceInfo.location || "N/A"}
                      </p>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        IP Address
                      </Label>
                      <p className="text-sm font-medium">
                        {deviceInfo.ipAddress || "N/A"}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Port
                      </Label>
                      <p className="text-sm font-medium">
                        {deviceInfo.port || "N/A"}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Status
                      </Label>
                      <div className="flex items-center gap-2">
                        <Badge
                          variant={
                            deviceStatus === "ONLINE"
                              ? "default"
                              : deviceStatus === "OFFLINE"
                              ? "destructive"
                              : "secondary"
                          }
                          className={
                            deviceStatus === "ONLINE"
                              ? "bg-green-500"
                              : deviceStatus === "OFFLINE"
                              ? "bg-red-500"
                              : "bg-gray-500"
                          }
                        >
                          {deviceStatus === "LOADING"
                            ? "Checking..."
                            : deviceStatus}
                        </Badge>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={checkDeviceStatus}
                          disabled={deviceStatus === "LOADING"}
                          className="text-xs"
                        >
                          <RefreshCw
                            className={`h-3 w-3 ${
                              deviceStatus === "LOADING" ? "animate-spin" : ""
                            }`}
                          />
                          Refresh
                        </Button>
                      </div>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Last Seen
                      </Label>
                      <p className="text-sm font-medium">
                        {deviceInfo.lastSeen || "N/A"}
                      </p>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  No device information available
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Device Users Tab */}
        <TabsContent value="users" className="space-y-4">
          {/* Device Status Warning */}
          {deviceStatus !== "ONLINE" && (
            <Card className="border-orange-200 bg-orange-50">
              <CardContent className="pt-2">
                <div className="flex flex-col sm:flex-row sm:items-center-safe gap-3">
                  <div className="flex-shrink-0">
                    <Badge
                      variant="secondary"
                      className={
                        deviceStatus === "OFFLINE"
                          ? "bg-red-100 text-rose-600"
                          : "bg-gray-100 text-gray-800"
                      }
                    >
                      {deviceStatus === "LOADING"
                        ? "Checking..."
                        : deviceStatus}
                    </Badge>
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-orange-800">
                      Device is currently {deviceStatus.toLowerCase()}
                    </p>
                    <p className="text-sm text-orange-600">
                      User management actions are only available when the device
                      is online. Please ensure the device is connected and try
                      refreshing the status.
                    </p>
                  </div>
                  <Button
                    size="sm"
                    variant="default"
                    onClick={checkDeviceStatus}
                    disabled={deviceStatus === "LOADING"}
                    className="text-xs"
                  >
                    <RefreshCw
                      className={`h-3 w-3 mr-1 ${
                        deviceStatus === "LOADING" ? "animate-spin" : ""
                      }`}
                    />
                    Refresh Status
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* User Management Forms */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <UserPlus className="h-5 w-5" />
                  Add User
                </CardTitle>
                <CardDescription>Add a new user to this device</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="userId">User ID</Label>
                    <Input
                      id="userId"
                      value={userFormData.userId}
                      onChange={(e) =>
                        setUserFormData((prev) => ({
                          ...prev,
                          userId: e.target.value,
                        }))
                      }
                      placeholder="Enter user ID"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="userName">User Name</Label>
                    <Input
                      id="userName"
                      value={userFormData.userName}
                      onChange={(e) =>
                        setUserFormData((prev) => ({
                          ...prev,
                          userName: e.target.value,
                        }))
                      }
                      placeholder="Enter user name"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="userType">User Type</Label>
                  <Select
                    value={userFormData.userType}
                    onValueChange={(value: "user" | "admin") =>
                      setUserFormData((prev) => ({ ...prev, userType: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="user">Regular User</SelectItem>
                      <SelectItem value="admin">Admin User</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Valid From</Label>
                    <DatePicker
                      value={userFormData.validFrom || undefined}
                      onChange={(date) =>
                        setUserFormData((prev) => ({
                          ...prev,
                          validFrom: date || null,
                        }))
                      }
                      placeholder="Select start date"
                      className="w-full"
                      dateFormat="MM/dd/yyyy"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Valid To</Label>
                    <DatePicker
                      value={userFormData.validTo || undefined}
                      onChange={(date) =>
                        setUserFormData((prev) => ({
                          ...prev,
                          validTo: date || null,
                        }))
                      }
                      placeholder="Select end date"
                      className="w-full"
                      dateFormat="MM/dd/yyyy"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="faceImage">Face Image</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      ref={fileInputRef}
                      type="file"
                      accept="image/*"
                      onChange={handleImageUpload}
                      className="flex-1"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => fileInputRef.current?.click()}
                    >
                      <Image className="h-4 w-4 mr-2" />
                      Upload
                    </Button>
                  </div>
                  {userFormData.faceData && (
                    <div className="flex items-center gap-2 text-sm text-green-600">
                      <CheckCircle className="h-4 w-4" />
                      Face image uploaded and compressed
                    </div>
                  )}
                </div>

                <Button
                  onClick={handleAddUser}
                  disabled={loading || !isDeviceOnline()}
                  className="w-full"
                >
                  {loading ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Adding User...
                    </>
                  ) : (
                    <>
                      <UserPlus className="h-4 w-4 mr-2" />
                      Add User
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>

            {/* Action Buttons */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  User Management Actions
                </CardTitle>
                <CardDescription>
                  Perform bulk operations on device users
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button
                  onClick={handleGetUserData}
                  disabled={loading || !isDeviceOnline()}
                  variant="outline"
                  className="w-full justify-start"
                >
                  {loading ? (
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Users className="h-4 w-4 mr-2" />
                  )}
                  Get User Data
                </Button>

                <Button
                  onClick={handleClearAllUsers}
                  disabled={loading || !isDeviceOnline()}
                  variant="destructive"
                  className="w-full justify-start"
                >
                  {loading ? (
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <XCircle className="h-4 w-4 mr-2" />
                  )}
                  Clear All Users
                </Button>

                <Button
                  onClick={handleClearAllAdmins}
                  disabled={loading || !isDeviceOnline()}
                  variant="destructive"
                  className="w-full justify-start"
                >
                  {loading ? (
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Shield className="h-4 w-4 mr-2" />
                  )}
                  Clear All Admins
                </Button>
              </CardContent>
            </Card>

            {/* User Data Table */}
            {userData.length > 0 && (
              <Card className="w-full col-span-2">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    Device Users ({userData.length})
                  </CardTitle>
                  <CardDescription>
                    Users currently registered on this device
                  </CardDescription>
                </CardHeader>
                <CardContent className="w-full">
                  <div className="w-full overflow-x-auto">
                    <table className="w-full border-collapse border border-gray-200">
                      <thead>
                        <tr className="bg-gray-50">
                          <th className="border border-gray-200 px-4 py-2 text-left">
                            ID
                          </th>
                          <th className="border border-gray-200 px-4 py-2 text-left">
                            Name
                          </th>
                          <th className="border border-gray-200 px-4 py-2 text-left">
                            Type
                          </th>
                          <th className="border border-gray-200 px-4 py-2 text-left">
                            Biometrics
                          </th>
                          <th className="border border-gray-200 px-4 py-2 text-left">
                            Status
                          </th>
                          <th className="border border-gray-200 px-4 py-2 text-left">
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {userData.map((user) => (
                          <tr key={user.id} className="hover:bg-gray-50">
                            <td className="border border-gray-200 px-4 py-2">
                              {user.enrollmentNo}
                            </td>
                            <td className="border border-gray-200 px-4 py-2">
                              {user.name}
                            </td>
                            <td className="border border-gray-200 px-4 py-2">
                              {user.isAdmin ? (
                                <Badge variant="secondary">Admin</Badge>
                              ) : (
                                <Badge variant="outline">User</Badge>
                              )}
                            </td>
                            <td className="border border-gray-200 px-4 py-2">
                              <div className="flex gap-1">
                                {user.biometrics.hasFace && (
                                  <Badge variant="outline" className="text-xs">
                                    Face
                                  </Badge>
                                )}
                                {user.biometrics.hasFingerprint && (
                                  <Badge variant="outline" className="text-xs">
                                    Finger
                                  </Badge>
                                )}
                                {user.biometrics.hasCard && (
                                  <Badge variant="outline" className="text-xs">
                                    Card
                                  </Badge>
                                )}
                                {user.biometrics.hasPassword && (
                                  <Badge variant="outline" className="text-xs">
                                    Password
                                  </Badge>
                                )}
                              </div>
                            </td>
                            <td className="border border-gray-200 px-4 py-2">
                              {user.isEnabled ? (
                                <Badge
                                  variant="default"
                                  className="bg-green-500"
                                >
                                  Enabled
                                </Badge>
                              ) : (
                                <Badge variant="destructive">Disabled</Badge>
                              )}
                            </td>
                            <td className="border border-gray-200 px-4 py-2">
                              <div className="flex gap-2">
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="text-xs"
                                  onClick={() =>
                                    handleToggleAdmin(
                                      user.enrollmentNo,
                                      user.isAdmin
                                    )
                                  }
                                  disabled={loading || !isDeviceOnline()}
                                >
                                  {user.isAdmin ? "Remove Admin" : "Make Admin"}
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="text-xs"
                                  onClick={() =>
                                    handleToggleUserStatus(
                                      user.enrollmentNo,
                                      user.isEnabled
                                    )
                                  }
                                  disabled={loading || !isDeviceOnline()}
                                >
                                  {user.isEnabled ? "Disable" : "Enable"}
                                </Button>
                                <Button
                                  size="sm"
                                  variant="secondary"
                                  className="text-xs"
                                  onClick={() => {
                                    setSetValidityDialog({
                                      open: true,
                                      userId: user.enrollmentNo,
                                      userName: user.name,
                                      fromDate: null,
                                      toDate: null,
                                    });
                                  }}
                                  disabled={loading || !isDeviceOnline()}
                                >
                                  Set Validity
                                </Button>
                                <Button
                                  size="sm"
                                  variant="destructive"
                                  className="text-xs"
                                  onClick={() =>
                                    handleDeleteUser(user.enrollmentNo)
                                  }
                                  disabled={loading || !isDeviceOnline()}
                                >
                                  Delete
                                </Button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        {/* Attendance Logs Tab */}
        <TabsContent value="logs" className="space-y-4">
          <AttendanceLogsView
            deviceId={deviceId}
            isDeviceOnline={isDeviceOnline}
          />
        </TabsContent>

        {/* Device Actions Tab */}
        <TabsContent value="actions" className="space-y-4">
          <DeviceActionsView
            deviceId={deviceId}
            isDeviceOnline={isDeviceOnline}
          />
        </TabsContent>
      </Tabs>

      {/* Set Validity Dialog */}
      <Dialog
        open={setValidityDialog.open}
        onOpenChange={(open) =>
          setSetValidityDialog((prev) => ({ ...prev, open }))
        }
      >
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Set User Validity</DialogTitle>
            <DialogDescription>
              Set validity period for user {setValidityDialog.userName} (ID:{" "}
              {setValidityDialog.userId})
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label>Valid From</Label>
              <DatePicker
                value={setValidityDialog.fromDate || undefined}
                onChange={(date) =>
                  setSetValidityDialog((prev) => ({
                    ...prev,
                    fromDate: date || null,
                  }))
                }
                placeholder="Select start date"
                className="w-full"
                dateFormat="MM/dd/yyyy"
              />
            </div>
            <div className="space-y-2">
              <Label>Valid To</Label>
              <DatePicker
                value={setValidityDialog.toDate || undefined}
                onChange={(date) =>
                  setSetValidityDialog((prev) => ({
                    ...prev,
                    toDate: date || null,
                  }))
                }
                placeholder="Select end date"
                className="w-full"
                dateFormat="MM/dd/yyyy"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() =>
                setSetValidityDialog((prev) => ({ ...prev, open: false }))
              }
            >
              Cancel
            </Button>
            <Button
              onClick={handleSetValidity}
              disabled={
                loading ||
                !setValidityDialog.fromDate ||
                !setValidityDialog.toDate
              }
            >
              {loading ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Setting...
                </>
              ) : (
                "Set Validity"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
