"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { RefreshCw, ArrowRight, Users } from "lucide-react";
import { toast } from "sonner";

interface Device {
  id: string;
  name: string;
  serialNumber: string;
  model: string;
  location: string;
  status: string;
  totalUsers: number;
  allocatedCompany?: string | null;
}

interface SyncDevicesModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  devices: Device[];
  onSuccess: () => void;
}

export function SyncDevicesModal({
  open,
  onOpenChange,
  devices,
  onSuccess,
}: SyncDevicesModalProps) {
  const [sourceDevice, setSourceDevice] = useState<string>("");
  const [targetDevices, setTargetDevices] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  // Show all devices for sync (user requested to show all devices)
  const availableDevices = devices;

  const handleTargetDeviceToggle = (deviceId: string) => {
    setTargetDevices((prev) =>
      prev.includes(deviceId)
        ? prev.filter((id) => id !== deviceId)
        : [...prev, deviceId]
    );
  };

  const handleSync = async () => {
    if (!sourceDevice) {
      toast.error("Please select a source device");
      return;
    }

    if (targetDevices.length === 0) {
      toast.error("Please select at least one target device");
      return;
    }

    if (targetDevices.includes(sourceDevice)) {
      toast.error("Source device cannot be a target device");
      return;
    }

    setLoading(true);
    try {
      const response = await fetch("/api/admin/devices/sync", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          sourceDeviceSerial: sourceDevice,
          destinationDeviceSerials: targetDevices,
          syncType: "users",
        }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        toast.success(data.message);
        onSuccess();
        onOpenChange(false);
        resetForm();
      } else {
        // Show detailed error information if available
        if (data.data?.results) {
          const failures = data.data.results.filter(
            (r: any) => r.status === "error"
          );
          if (failures.length > 0) {
            toast.error(
              `Sync failed for some devices: ${failures
                .map((f: any) => f.destinationDevice)
                .join(", ")}`
            );
          } else {
            toast.success(data.message);
            onSuccess();
            onOpenChange(false);
            resetForm();
          }
        } else {
          toast.error(data.error || "Failed to sync devices");
        }
      }
    } catch (error) {
      console.error("Sync error:", error);
      toast.error("Failed to sync devices");
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setSourceDevice("");
    setTargetDevices([]);
  };

  const getSourceDevice = () =>
    availableDevices.find((d) => d.serialNumber === sourceDevice);
  const getTargetDevicesList = () =>
    availableDevices.filter((d) => targetDevices.includes(d.serialNumber));

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <RefreshCw className="h-5 w-5" />
            Sync Device Users
          </DialogTitle>
          <DialogDescription>
            Copy user data from a source device to one or more target devices.
            This will sync all users, biometric data, and settings from the
            source device.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Source Device Selection */}
          <div className="space-y-3">
            <Label className="text-base font-medium">Source Device</Label>
            <Select value={sourceDevice} onValueChange={setSourceDevice}>
              <SelectTrigger>
                <SelectValue placeholder="Select source device to copy data from" />
              </SelectTrigger>
              <SelectContent>
                {availableDevices.map((device) => (
                  <SelectItem
                    key={device.serialNumber}
                    value={device.serialNumber}
                  >
                    <div className="flex items-center justify-between w-full">
                      <span>{device.name}</span>
                      <div className="flex items-center gap-2 ml-2">
                        <Badge variant="outline" className="text-xs">
                          <Users className="h-3 w-3 mr-1" />
                          {device.totalUsers} users
                        </Badge>
                        <Badge variant="secondary" className="text-xs">
                          {device.serialNumber}
                        </Badge>
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {sourceDevice && (
              <Card className="bg-blue-50 border-blue-200">
                <CardContent className="pt-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-blue-900">
                        {getSourceDevice()?.name}
                      </p>
                      <p className="text-sm text-blue-700">
                        {getSourceDevice()?.location} •{" "}
                        {getSourceDevice()?.totalUsers} users
                      </p>
                    </div>
                    <Badge className="bg-blue-100 text-blue-800">Source</Badge>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Target Devices Selection */}
          <div className="space-y-3">
            <Label className="text-base font-medium">Target Devices</Label>
            <p className="text-sm text-gray-600">
              Select devices to sync user data to. User data from the source
              device will be copied to these devices.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-h-60 overflow-y-auto">
              {availableDevices
                .filter((device) => device.serialNumber !== sourceDevice)
                .map((device) => (
                  <Card
                    key={device.serialNumber}
                    className={`cursor-pointer transition-colors ${
                      targetDevices.includes(device.serialNumber)
                        ? "bg-green-50 border-green-200"
                        : "hover:bg-gray-50"
                    }`}
                    onClick={() =>
                      handleTargetDeviceToggle(device.serialNumber)
                    }
                  >
                    <CardContent className="pt-4">
                      <div className="flex items-center space-x-3">
                        <Checkbox
                          checked={targetDevices.includes(device.serialNumber)}
                          onChange={() =>
                            handleTargetDeviceToggle(device.serialNumber)
                          }
                        />
                        <div className="flex-1">
                          <p className="font-medium">{device.name}</p>
                          <p className="text-sm text-gray-600">
                            {device.location} • {device.totalUsers} users
                          </p>
                          <Badge variant="outline" className="text-xs mt-1">
                            {device.serialNumber}
                          </Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
            </div>

            {targetDevices.length > 0 && (
              <Card className="bg-green-50 border-green-200">
                <CardContent className="pt-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Badge className="bg-green-100 text-green-800">
                      {targetDevices.length} Target Device(s)
                    </Badge>
                  </div>
                  <div className="text-sm text-green-700">
                    {getTargetDevicesList()
                      .map((device) => device.name)
                      .join(", ")}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sync Preview */}
          {sourceDevice && targetDevices.length > 0 && (
            <Card className="bg-yellow-50 border-yellow-200">
              <CardContent className="pt-4">
                <div className="flex items-center justify-center gap-4">
                  <div className="text-center">
                    <Badge className="bg-blue-100 text-blue-800 mb-1">
                      Source
                    </Badge>
                    <p className="text-sm font-medium">
                      {getSourceDevice()?.name}
                    </p>
                  </div>
                  <ArrowRight className="h-5 w-5 text-yellow-600" />
                  <div className="text-center">
                    <Badge className="bg-green-100 text-green-800 mb-1">
                      {targetDevices.length} Target(s)
                    </Badge>
                    <p className="text-sm font-medium">Selected Devices</p>
                  </div>
                </div>
                <p className="text-center text-sm text-yellow-700 mt-2">
                  User data will be copied from source to all target devices
                </p>
              </CardContent>
            </Card>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleSync}
            disabled={loading || !sourceDevice || targetDevices.length === 0}
          >
            {loading ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Syncing...
              </>
            ) : (
              <>
                <RefreshCw className="h-4 w-4 mr-2" />
                Sync Devices
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
