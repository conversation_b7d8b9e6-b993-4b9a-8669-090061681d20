"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Smartphone, CheckCircle, AlertCircle, Activity } from "lucide-react";

interface Device {
  id: string;
  name: string;
  serialNumber: string;
  status: string;
  allocatedCompany?: string | null;
}

interface DeviceStatsCardsProps {
  devices: Device[];
  deviceStatuses: Record<string, string>;
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
  loading?: boolean;
}

export function DeviceStatsCards({
  devices,
  deviceStatuses,
  pagination,
  loading = false,
}: DeviceStatsCardsProps) {
  const totalDevices = pagination.total;
  const onlineDevices = Object.values(deviceStatuses).filter(
    (status) => status === "ONLINE"
  ).length;
  const offlineDevices = Object.values(deviceStatuses).filter(
    (status) => status === "OFFLINE"
  ).length;
  const allocatedDevices = devices.filter((d) => d.allocatedCompany).length;

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Devices</CardTitle>
          <Smartphone className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{totalDevices}</div>
          <p className="text-xs text-muted-foreground">Registered in system</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Online Devices</CardTitle>
          <Activity className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">
            {onlineDevices}
          </div>
          <p className="text-xs text-muted-foreground">Currently active</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Offline Devices</CardTitle>
          <AlertCircle className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-red-600">
            {offlineDevices}
          </div>
          <p className="text-xs text-muted-foreground">Need attention</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Allocated Devices
          </CardTitle>
          <CheckCircle className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-blue-600">
            {allocatedDevices}
          </div>
          <p className="text-xs text-muted-foreground">Assigned to companies</p>
        </CardContent>
      </Card>
    </div>
  );
}
