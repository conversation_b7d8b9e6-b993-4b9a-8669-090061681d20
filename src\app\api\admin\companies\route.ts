import { NextRequest, NextResponse } from "next/server";
import { verifyJWT } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { createCompanySchema } from "@/lib/validations/company";
import {
  hashPassword,
  generateLoginToken,
  hashLoginToken,
  generateRandomPassword,
} from "@/lib/auth";

// GET /api/admin/companies - List all companies
export async function GET(req: NextRequest) {
  try {
    // Authentication check
    const token = req.cookies.get("auth-token")?.value;
    if (!token) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const user = verifyJWT(token);
    if (!user || user.role !== "admin") {
      return NextResponse.json(
        { error: "Admin access required" },
        { status: 403 }
      );
    }
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("pageSize") || "10");
    const query = searchParams.get("query") || "";
    const status = searchParams.get("status");
    const userType = searchParams.get("userType");

    const skip = (page - 1) * pageSize;

    try {
      // Build where clause
      const where: any = {};

      if (query) {
        where.OR = [
          { name: { contains: query, mode: "insensitive" } },
          { email: { contains: query, mode: "insensitive" } },
          { organizationId: { contains: query, mode: "insensitive" } },
        ];
      }

      if (status) {
        where.status = status;
      }

      if (userType) {
        where.userType = userType;
      }

      // Get companies with pagination
      const [companies, total] = await Promise.all([
        prisma.company.findMany({
          where,
          skip,
          take: pageSize,
          orderBy: { createdAt: "desc" },
          include: {
            allocations: {
              select: {
                deviceSerialNo: true,
              },
            },
          },
        }),
        prisma.company.count({ where }),
      ]);

      return NextResponse.json({
        success: true,
        data: companies,
        pagination: {
          page,
          pageSize,
          total,
          totalPages: Math.ceil(total / pageSize),
        },
      });
    } catch (dbError) {
      return NextResponse.json(
        {
          error:
            "Database connection failed. Please ensure PostgreSQL is running.",
          details: "Cannot fetch companies without database connection.",
        },
        { status: 503 }
      );
    }
  } catch (error) {
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/admin/companies - Create new company
export async function POST(req: NextRequest) {
  try {
    // Authentication check
    const token = req.cookies.get("auth-token")?.value;
    if (!token) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const user = verifyJWT(token);
    if (!user || user.role !== "admin") {
      return NextResponse.json(
        { error: "Admin access required" },
        { status: 403 }
      );
    }
    const body = await req.json();
    const validatedData = createCompanySchema.parse(body);

    try {
      // Check if email or organization ID already exists
      const existingCompany = await prisma.company.findFirst({
        where: {
          OR: [
            { email: validatedData.email },
            { organizationId: validatedData.organizationId },
          ],
        },
      });

      if (existingCompany) {
        return NextResponse.json(
          {
            error: "Company with this email or organization ID already exists",
          },
          { status: 400 }
        );
      }

      // Generate password if not provided
      const password = validatedData.password || generateRandomPassword();

      // Hash password
      const passwordHash = await hashPassword(password);

      // Generate login token
      const loginToken = generateLoginToken();
      const loginTokenHash = await hashLoginToken(loginToken);

      // Calculate expiration date
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + validatedData.validity);

      // Create company
      const company = await prisma.company.create({
        data: {
          name: validatedData.name,
          organizationId: validatedData.organizationId,
          email: validatedData.email,
          passwordHash,
          loginTokenHash,
          loginToken, // Store the plain token for admin display
          userType: validatedData.userType,
          expiresAt,
          description: validatedData.description,
          externalApiEndpoint: validatedData.externalApiEndpoint,
        },
      });

      return NextResponse.json({
        success: true,
        data: {
          company,
          loginToken, // Return the plain token for admin to share
          password: validatedData.password ? undefined : password, // Return generated password if applicable
        },
      });
    } catch (dbError) {
      return NextResponse.json(
        {
          error:
            "Database connection failed. Cannot create company without database.",
          details: "Please ensure PostgreSQL is running and try again.",
        },
        { status: 503 }
      );
    }
  } catch (error) {
    if (error instanceof Error && error.name === "ZodError") {
      return NextResponse.json(
        { error: "Invalid input data" },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
