"use client";

import { useState, useEffect, useCallback, useMemo } from "react";
import { toast } from "react-toastify";
import { useDeviceStore, Device } from "@/stores/useDeviceStore";
import {
  useDevicesQuery,
  useDeviceActionMutation,
  useDeviceAllocationMutation,
  useDeviceSyncMutation,
  useCreateDeviceMutation,
  useUpdateDeviceMutation,
  useDeleteDeviceMutation,
} from "@/hooks/queries/useDevicesQuery";
import {
  useDeviceStatusesQuery,
  useRefreshDeviceStatusesMutation,
} from "@/hooks/queries/useDeviceStatusQuery";

interface TimeZone {
  id: string;
  name: string;
}

interface DeviceFormData {
  deviceSlno: string;
  deviceName: string;
  modelNo: string;
  timeZoneId: string;
  location: string;
  endpointUrl: string;
}

export function useDeviceManagement() {
  // Zustand store state
  const {
    filters,
    pagination,
    selectedDevices,
    deviceStatuses,
    statusLoading,
    statusLastFetched,
    setSearch,
    setStatus,
    setAllocation,
    setSorting,
    setPage,
    setPageSize,
    setPagination,
    setSelectedDevices,
    toggleDeviceSelection,
    selectAllDevices,
    clearSelection,
    resetFilters,
    setMultipleDeviceStatuses,
    setStatusLoading,
    setStatusLastFetched,
  } = useDeviceStore();

  // Local UI state
  const [timeZones, setTimeZones] = useState<TimeZone[]>([]);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showAllocationModal, setShowAllocationModal] = useState(false);
  const [allocationMode, setAllocationMode] = useState<
    "allocate" | "deallocate"
  >("allocate");
  const [showStatusTooltip, setShowStatusTooltip] = useState(false);
  const [isHydrated, setIsHydrated] = useState(false);
  const [renderKey, setRenderKey] = useState(0);
  const [statusHistory, setStatusHistory] = useState<{
    [key: string]: string[];
  }>({});

  const [deviceFormData, setDeviceFormData] = useState<DeviceFormData>({
    deviceSlno: "",
    deviceName: "",
    modelNo: "",
    timeZoneId: "",
    location: "",
    endpointUrl: "",
  });

  // TanStack Query hooks
  const {
    data: queryData,
    isLoading,
    error,
    refetch,
  } = useDevicesQuery(filters, pagination);

  // Get device serial numbers for status fetching
  const deviceSerialNumbers = useMemo(
    () => queryData?.devices?.map((device) => device.serialNumber) || [],
    [queryData?.devices]
  );

  const { data: statusData, isLoading: statusQueryLoading } =
    useDeviceStatusesQuery(deviceSerialNumbers, deviceSerialNumbers.length > 0);

  // Mutations
  const deviceActionMutation = useDeviceActionMutation();
  const deviceAllocationMutation = useDeviceAllocationMutation();
  const deviceSyncMutation = useDeviceSyncMutation();
  const createDeviceMutation = useCreateDeviceMutation();
  const updateDeviceMutation = useUpdateDeviceMutation();
  const deleteDeviceMutation = useDeleteDeviceMutation();
  const refreshStatusMutation = useRefreshDeviceStatusesMutation();

  // Hydration effect
  useEffect(() => {
    setIsHydrated(true);
  }, []);

  // Update pagination when query data changes
  useEffect(() => {
    if (queryData?.pagination) {
      setPagination(queryData.pagination);
    }
  }, [queryData?.pagination, setPagination]);

  // Update device statuses when status data changes
  useEffect(() => {
    if (statusData?.statusMap) {
      setMultipleDeviceStatuses(statusData.statusMap);
    }
  }, [statusData, setMultipleDeviceStatuses]);

  // Update status loading state
  useEffect(() => {
    setStatusLoading(statusQueryLoading);
  }, [statusQueryLoading, setStatusLoading]);

  // Show tooltip when status loading completes
  useEffect(() => {
    if (!statusLoading && statusLastFetched) {
      setShowStatusTooltip(true);
      const timer = setTimeout(() => {
        setShowStatusTooltip(false);
      }, 4000);
      return () => clearTimeout(timer);
    }
  }, [statusLoading, statusLastFetched]);

  // Track status changes and force re-render
  useEffect(() => {
    if (Object.keys(deviceStatuses).length > 0) {
      // Track status changes for debugging
      Object.entries(deviceStatuses).forEach(([serialNumber, status]) => {
        setStatusHistory((prev) => {
          const history = prev[serialNumber] || [];
          const lastStatus = history[history.length - 1];

          if (lastStatus !== status) {
            const newHistory = [...history, status].slice(-5); // Keep last 5 status changes
            return { ...prev, [serialNumber]: newHistory };
          }
          return prev;
        });
      });

      // Force re-render by updating render key
      setRenderKey((prev) => prev + 1);
    }
  }, [deviceStatuses]);

  // Handle errors in useEffect to avoid state updates during render
  useEffect(() => {
    if (error) {
      toast.error(error.message || "Failed to fetch devices");
    }
  }, [error]);

  // Handlers
  const handlePageChange = useCallback(
    (page: number) => {
      setPage(page);
    },
    [setPage]
  );

  const handleSearch = useCallback(
    (search: string) => {
      setSearch(search);
    },
    [setSearch]
  );

  const handleSort = useCallback(
    (column: string) => {
      setSorting(column);
    },
    [setSorting]
  );

  const handleRefreshAllDevicesStatus = useCallback(async () => {
    if (deviceSerialNumbers.length === 0) return;

    try {
      setStatusLoading(true);
      await refreshStatusMutation.mutateAsync(deviceSerialNumbers);
      setStatusLastFetched(Date.now());
      toast.success("Device statuses refreshed successfully");
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to refresh device statuses";
      toast.error(errorMessage);
    } finally {
      setStatusLoading(false);
    }
  }, [
    deviceSerialNumbers,
    refreshStatusMutation,
    setStatusLoading,
    setStatusLastFetched,
  ]);

  const handleDeviceAction = useCallback(
    async (
      action: string,
      deviceSlno: string,
      additionalData?: Record<string, unknown>
    ) => {
      try {
        const result = await deviceActionMutation.mutateAsync({
          action,
          deviceSlno,
          ...additionalData,
        });

        // Handle special actions that should open modals
        if (result.action === "openAllocationModal") {
          setSelectedDevices([
            { id: deviceSlno, serialNumber: deviceSlno } as Device,
          ]);
          setAllocationMode("allocate");
          setShowAllocationModal(true);
          toast.info("Opening allocation modal...");
          return;
        }

        if (result.action === "openDeallocationModal") {
          setSelectedDevices([
            { id: deviceSlno, serialNumber: deviceSlno } as Device,
          ]);
          setAllocationMode("deallocate");
          setShowAllocationModal(true);
          toast.info("Opening deallocation modal...");
          return;
        }

        if (result.action === "openEditModal") {
          // TODO: Implement edit modal
          toast.info(
            "Edit functionality will be implemented in the edit modal"
          );
          return;
        }

        toast.success(result.message || `${action} completed successfully`);
      } catch (error: unknown) {
        const errorMessage =
          error instanceof Error
            ? error.message
            : `Failed to perform ${action}`;
        toast.error(errorMessage);
      }
    },
    [
      deviceActionMutation,
      setSelectedDevices,
      setAllocationMode,
      setShowAllocationModal,
    ]
  );

  const handleBulkDelete = useCallback(
    async (deviceSerialNumbers: string[]) => {
      try {
        const response = await fetch("/api/admin/devices/bulk-delete", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ deviceSerialNumbers }),
        });

        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.error || "Failed to delete devices");
        }

        // Refresh devices list
        await refetch();

        // Clear selected devices
        setSelectedDevices([]);
      } catch (error) {
        throw error;
      }
    },
    [refetch, setSelectedDevices]
  );

  return {
    // State from TanStack Query
    devices: queryData?.devices || [],
    pagination,
    loading: isLoading,
    error,

    // State from Zustand store
    filters,
    selectedDevices,
    deviceStatuses,
    statusLoading,
    statusLastFetched,

    // Local UI state
    timeZones,
    showAddDialog,
    showAllocationModal,
    allocationMode,
    showStatusTooltip,
    isHydrated,
    renderKey,
    statusHistory,
    deviceFormData,

    // Store actions
    setSearch,
    setStatus,
    setAllocation,
    setSorting,
    setPage,
    setPageSize,
    setSelectedDevices,
    toggleDeviceSelection,
    selectAllDevices,
    clearSelection,
    resetFilters,

    // Local setters
    setTimeZones,
    setShowAddDialog,
    setShowAllocationModal,
    setAllocationMode,
    setDeviceFormData,

    // Handlers
    handlePageChange,
    handleSearch,
    handleSort,
    handleRefreshAllDevicesStatus,
    handleDeviceAction,
    handleBulkDelete,

    // TanStack Query actions
    refetch,
    createDevice: createDeviceMutation.mutate,
    updateDevice: updateDeviceMutation.mutate,
    deleteDevice: deleteDeviceMutation.mutate,
    allocateDevices: deviceAllocationMutation.mutate,
    syncDevices: deviceSyncMutation.mutate,

    // Mutation states
    isCreating: createDeviceMutation.isPending,
    isUpdating: updateDeviceMutation.isPending,
    isDeleting: deleteDeviceMutation.isPending,
    isAllocating: deviceAllocationMutation.isPending,
    isSyncing: deviceSyncMutation.isPending,
    isRefreshingStatus: refreshStatusMutation.isPending,
  };
}
