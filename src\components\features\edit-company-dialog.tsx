"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Save, X } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import { Company } from "@/stores/useCompanyStore";
import { useUpdateCompanyMutation } from "@/hooks/queries/useCompaniesQuery";
import DatePicker from "@/components/DatePicker";

interface EditCompanyDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  company: Company | null;
}

interface EditCompanyData {
  name: string;
  email: string;
  userType: string;
  status: string;
  expiresAt: Date;
  description: string;
  externalApiEndpoint?: string;
}

export function EditCompanyDialog({
  open,
  onOpenChange,
  company,
}: EditCompanyDialogProps) {
  const [formData, setFormData] = useState<EditCompanyData>({
    name: "",
    email: "",
    userType: "API_USER",
    status: "ACTIVE",
    expiresAt: new Date(),
    description: "",
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  const updateCompanyMutation = useUpdateCompanyMutation();

  // Initialize form data when company changes
  useEffect(() => {
    if (company) {
      setFormData({
        name: company.name,
        email: company.email,
        userType: company.userType,
        status: company.status,
        expiresAt: new Date(company.expiresAt),
        description: company.description || "",
        externalApiEndpoint: company.externalApiEndpoint || "",
      });
      setErrors({});
    }
  }, [company]);

  const handleInputChange = (
    field: keyof EditCompanyData,
    value: string | Date
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = "Company name is required";
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    if (formData.expiresAt <= new Date()) {
      newErrors.expiresAt = "Expiry date must be in the future";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!company || !validateForm()) {
      return;
    }

    try {
      await updateCompanyMutation.mutateAsync({
        id: company.id,
        ...formData,
        expiresAt: formData.expiresAt.toISOString(),
      });

      toast.success("Company updated successfully");
      onOpenChange(false);
    } catch (error: any) {
      const errorMessage =
        error.response?.data?.error || "Failed to update company";
      toast.error(errorMessage);
    }
  };

  const handleCancel = () => {
    onOpenChange(false);
    setErrors({});
  };

  if (!company) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Edit Company</DialogTitle>
          <DialogDescription>
            Update company information and settings
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Company Name */}
          <div className="space-y-2">
            <Label htmlFor="name">Company Name</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              className={errors.name ? "border-red-500" : ""}
              disabled={updateCompanyMutation.isPending}
            />
            {errors.name && (
              <p className="text-sm text-red-600">{errors.name}</p>
            )}
          </div>

          {/* Email */}
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange("email", e.target.value)}
              className={errors.email ? "border-red-500" : ""}
              disabled={updateCompanyMutation.isPending}
            />
            {errors.email && (
              <p className="text-sm text-red-600">{errors.email}</p>
            )}
          </div>

          {/* User Type */}
          <div className="space-y-2">
            <Label htmlFor="userType">User Type</Label>
            <Select
              value={formData.userType}
              onValueChange={(value) => handleInputChange("userType", value)}
              disabled={updateCompanyMutation.isPending}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="API_USER">API User</SelectItem>
                <SelectItem value="EMPLOYEE_MANAGEMENT">
                  Employee Management
                </SelectItem>
                <SelectItem value="SCHOOL_MANAGEMENT">
                  School Management
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Status */}
          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select
              value={formData.status}
              onValueChange={(value) => handleInputChange("status", value)}
              disabled={updateCompanyMutation.isPending}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ACTIVE">Active</SelectItem>
                <SelectItem value="DEACTIVATED">Deactivated</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Expiry Date */}
          <div className="space-y-2">
            <Label>Expiry Date</Label>
            <DatePicker
              value={formData.expiresAt}
              onChange={(date) =>
                handleInputChange("expiresAt", date || new Date())
              }
              placeholder="Select expiry date"
              className="w-full"
              disabled={updateCompanyMutation.isPending}
            />
            {errors.expiresAt && (
              <p className="text-sm text-red-600">{errors.expiresAt}</p>
            )}
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange("description", e.target.value)}
              placeholder="Optional description..."
              rows={3}
              disabled={updateCompanyMutation.isPending}
            />
          </div>

          {/* External API Endpoint */}
          <div className="space-y-2">
            <Label htmlFor="externalApiEndpoint">External API Endpoint</Label>
            <Input
              id="externalApiEndpoint"
              value={formData.externalApiEndpoint}
              onChange={(e) => handleInputChange("externalApiEndpoint", e.target.value)}
              placeholder="Optional external API endpoint..."
              disabled={updateCompanyMutation.isPending}
            />
          </div>

          {/* Actions */}
          <div className="flex space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={updateCompanyMutation.isPending}
              className="flex-1"
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={updateCompanyMutation.isPending}
              className="flex-1"
            >
              <Save className="h-4 w-4 mr-2" />
              {updateCompanyMutation.isPending ? "Saving..." : "Save Changes"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
