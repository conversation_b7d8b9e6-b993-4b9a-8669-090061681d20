import { NextRequest, NextResponse } from 'next/server'
import { withAuth, AuthenticatedRequest } from '@/lib/auth/middleware'
import { prisma } from '@/lib/prisma'
import { externalDeviceAPI } from '@/lib/api/external-device-api'

interface ActivityItem {
  id: string
  type: 'attendance_in' | 'attendance_out' | 'device_sync' | 'user_added' | 'system_alert'
  title: string
  description: string
  timestamp: string
  status: 'success' | 'warning' | 'error' | 'info'
  deviceSerial?: string
  employeeId?: string
}

async function getCompanyActivity(req: AuthenticatedRequest) {
  try {
    const user = req.user!
    
    if (user.role !== 'company') {
      return NextResponse.json(
        { error: 'Company access required' },
        { status: 403 }
      )
    }

    const activities: ActivityItem[] = []

    try {
      // Get company details and allocated devices
      const company = await prisma.company.findUnique({
        where: { id: user.companyId },
        include: {
          allocations: {
            select: {
              deviceSerialNo: true,
              createdAt: true
            }
          }
        }
      })

      if (!company) {
        return NextResponse.json(
          { error: 'Company not found' },
          { status: 404 }
        )
      }

      // Get recent attendance logs from all allocated devices
      const last24Hours = new Date(Date.now() - 24 * 60 * 60 * 1000)
      const now = new Date()

      for (const allocation of company.allocations) {
        try {
          const attendanceResponse = await externalDeviceAPI.getAttendanceLog([{
            DEVICESLNO: allocation.deviceSerialNo,
            FROMDATE: last24Hours.toISOString().slice(0, 16).replace('T', ' '),
            TODATE: now.toISOString().slice(0, 16).replace('T', ' ')
          }])

          if (attendanceResponse.status === '200' && attendanceResponse.data) {
            attendanceResponse.data.forEach((record: any) => {
              const isCheckIn = record.inout_value === 0 || record.inout_value === '0'
              activities.push({
                id: `attendance-${allocation.deviceSerialNo}-${record.EnrollmentNo}-${record.PunchDateTime}`,
                type: isCheckIn ? 'attendance_in' : 'attendance_out',
                title: isCheckIn ? 'Employee Check-in' : 'Employee Check-out',
                description: `Employee ${record.EnrollmentNo} ${isCheckIn ? 'checked in' : 'checked out'}`,
                timestamp: record.PunchDateTime,
                status: 'success',
                deviceSerial: allocation.deviceSerialNo,
                employeeId: record.EnrollmentNo
              })
            })
          }
        } catch (error) {
          console.warn(`Failed to get attendance for device ${allocation.deviceSerialNo}:`, error)
          
          // Add error activity
          activities.push({
            id: `error-${allocation.deviceSerialNo}-${Date.now()}`,
            type: 'system_alert',
            title: 'Device Communication Error',
            description: `Failed to sync with device ${allocation.deviceSerialNo}`,
            timestamp: new Date().toISOString(),
            status: 'error',
            deviceSerial: allocation.deviceSerialNo
          })
        }
      }

      // Add device allocation activities
      company.allocations.forEach(allocation => {
        activities.push({
          id: `allocation-${allocation.deviceSerialNo}`,
          type: 'device_sync',
          title: 'Device Allocated',
          description: `Device ${allocation.deviceSerialNo} was allocated to your company`,
          timestamp: allocation.createdAt.toISOString(),
          status: 'info',
          deviceSerial: allocation.deviceSerialNo
        })
      })

      // Check device status and add alerts for offline devices
      for (const allocation of company.allocations) {
        try {
          const statusResponse = await externalDeviceAPI.getDeviceOnlineStatus(allocation.deviceSerialNo)
          if (statusResponse.status !== '200') {
            activities.push({
              id: `offline-${allocation.deviceSerialNo}`,
              type: 'system_alert',
              title: 'Device Offline',
              description: `Device ${allocation.deviceSerialNo} is currently offline`,
              timestamp: new Date().toISOString(),
              status: 'warning',
              deviceSerial: allocation.deviceSerialNo
            })
          }
        } catch (error) {
          activities.push({
            id: `unreachable-${allocation.deviceSerialNo}`,
            type: 'system_alert',
            title: 'Device Unreachable',
            description: `Cannot connect to device ${allocation.deviceSerialNo}`,
            timestamp: new Date().toISOString(),
            status: 'error',
            deviceSerial: allocation.deviceSerialNo
          })
        }
      }

    } catch (dbError) {
      console.warn('Database not available for activity data:', dbError)
      
      // Add system message when database is not available
      activities.push({
        id: 'system-db-unavailable',
        type: 'system_alert',
        title: 'System Status',
        description: 'Database connection unavailable - limited activity data',
        timestamp: new Date().toISOString(),
        status: 'warning'
      })
    }

    // Sort activities by timestamp (most recent first)
    activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())

    // Return only the most recent 15 activities
    const recentActivities = activities.slice(0, 15)

    return NextResponse.json({
      success: true,
      data: recentActivities
    })

  } catch (error) {
    console.error('Get company activity error:', error)

    return NextResponse.json(
      { error: 'Failed to fetch company activity' },
      { status: 500 }
    )
  }
}

export const GET = withAuth(getCompanyActivity)
